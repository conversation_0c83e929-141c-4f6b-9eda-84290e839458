#!/bin/bash

# Android NDK Setup Script for Colony
# This script configures the Android NDK environment for cross-compiling RocksDB and other C++ dependencies

set -e

echo "🔧 Setting up Android NDK for Colony compilation..."

# Check if Android NDK is installed
if [ -z "$ANDROID_NDK_ROOT" ]; then
    echo "❌ ANDROID_NDK_ROOT environment variable is not set"
    echo "Please install Android NDK and set ANDROID_NDK_ROOT"
    echo ""
    echo "Installation options:"
    echo "1. Via Android Studio: Tools → SDK Manager → SDK Tools → NDK"
    echo "2. Direct download: https://developer.android.com/ndk/downloads"
    echo ""
    echo "Then set the environment variable:"
    echo "export ANDROID_NDK_ROOT=/path/to/android-ndk"
    exit 1
fi

if [ ! -d "$ANDROID_NDK_ROOT" ]; then
    echo "❌ Android NDK directory not found: $ANDROID_NDK_ROOT"
    exit 1
fi

echo "✅ Found Android NDK at: $ANDROID_NDK_ROOT"

# Determine NDK version and toolchain path
NDK_VERSION=$(cat "$ANDROID_NDK_ROOT/source.properties" | grep "Pkg.Revision" | cut -d'=' -f2 | tr -d ' ')
echo "📦 NDK Version: $NDK_VERSION"

# Set up toolchain paths
TOOLCHAIN_DIR="$ANDROID_NDK_ROOT/toolchains/llvm/prebuilt/linux-x86_64"

if [ ! -d "$TOOLCHAIN_DIR" ]; then
    echo "❌ Toolchain directory not found: $TOOLCHAIN_DIR"
    echo "Your NDK installation might be incomplete or for a different platform"
    exit 1
fi

echo "🔨 Toolchain directory: $TOOLCHAIN_DIR"

# Set up environment variables for cross-compilation
export PATH="$TOOLCHAIN_DIR/bin:$PATH"

# API level for Android (minimum supported by Tauri)
API_LEVEL=24

# Set up C/C++ compiler environment variables for each target
echo "🎯 Configuring cross-compilation environment..."

# ARM64 (aarch64)
export CC_aarch64_linux_android="$TOOLCHAIN_DIR/bin/aarch64-linux-android${API_LEVEL}-clang"
export CXX_aarch64_linux_android="$TOOLCHAIN_DIR/bin/aarch64-linux-android${API_LEVEL}-clang++"
export AR_aarch64_linux_android="$TOOLCHAIN_DIR/bin/llvm-ar"
export CARGO_TARGET_AARCH64_LINUX_ANDROID_LINKER="$TOOLCHAIN_DIR/bin/aarch64-linux-android${API_LEVEL}-clang"

# ARM32 (armv7)
export CC_armv7_linux_androideabi="$TOOLCHAIN_DIR/bin/armv7a-linux-androideabi${API_LEVEL}-clang"
export CXX_armv7_linux_androideabi="$TOOLCHAIN_DIR/bin/armv7a-linux-androideabi${API_LEVEL}-clang++"
export AR_armv7_linux_androideabi="$TOOLCHAIN_DIR/bin/llvm-ar"
export CARGO_TARGET_ARMV7_LINUX_ANDROIDEABI_LINKER="$TOOLCHAIN_DIR/bin/armv7a-linux-androideabi${API_LEVEL}-clang"

# x86
export CC_i686_linux_android="$TOOLCHAIN_DIR/bin/i686-linux-android${API_LEVEL}-clang"
export CXX_i686_linux_android="$TOOLCHAIN_DIR/bin/i686-linux-android${API_LEVEL}-clang++"
export AR_i686_linux_android="$TOOLCHAIN_DIR/bin/llvm-ar"
export CARGO_TARGET_I686_LINUX_ANDROID_LINKER="$TOOLCHAIN_DIR/bin/i686-linux-android${API_LEVEL}-clang"

# x86_64
export CC_x86_64_linux_android="$TOOLCHAIN_DIR/bin/x86_64-linux-android${API_LEVEL}-clang"
export CXX_x86_64_linux_android="$TOOLCHAIN_DIR/bin/x86_64-linux-android${API_LEVEL}-clang++"
export AR_x86_64_linux_android="$TOOLCHAIN_DIR/bin/llvm-ar"
export CARGO_TARGET_X86_64_LINUX_ANDROID_LINKER="$TOOLCHAIN_DIR/bin/x86_64-linux-android${API_LEVEL}-clang"

# Verify toolchain files exist
echo "🔍 Verifying toolchain files..."
for target in aarch64 armv7a i686 x86_64; do
    if [ "$target" = "aarch64" ]; then
        compiler="$TOOLCHAIN_DIR/bin/aarch64-linux-android${API_LEVEL}-clang"
    elif [ "$target" = "armv7a" ]; then
        compiler="$TOOLCHAIN_DIR/bin/armv7a-linux-androideabi${API_LEVEL}-clang"
    elif [ "$target" = "i686" ]; then
        compiler="$TOOLCHAIN_DIR/bin/i686-linux-android${API_LEVEL}-clang"
    else
        compiler="$TOOLCHAIN_DIR/bin/x86_64-linux-android${API_LEVEL}-clang"
    fi
    
    if [ -f "$compiler" ]; then
        echo "  ✅ $target compiler found"
    else
        echo "  ❌ $target compiler not found: $compiler"
    fi
done

echo ""
echo "🚀 Android NDK environment configured successfully!"
echo ""
echo "Environment variables set:"
echo "  ANDROID_NDK_ROOT=$ANDROID_NDK_ROOT"
echo "  PATH includes: $TOOLCHAIN_DIR/bin"
echo ""
echo "You can now build for Android with:"
echo "  cargo build --target aarch64-linux-android"
echo "  cargo tauri android dev"
echo ""
echo "To make these settings permanent, add them to your shell profile:"
echo "  echo 'export ANDROID_NDK_ROOT=$ANDROID_NDK_ROOT' >> ~/.bashrc"
echo "  echo 'export PATH=\"$TOOLCHAIN_DIR/bin:\$PATH\"' >> ~/.bashrc"
