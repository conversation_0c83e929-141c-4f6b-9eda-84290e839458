#!/bin/bash

# Test macOS Signing Script
# This script helps test the macOS signing process locally before running in GitHub Actions

set -e

echo "🍎 Testing macOS Signing Process for Colony"
echo "=========================================="

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ This script must be run on macOS"
    exit 1
fi

# Check if Xcode command line tools are installed
if ! command -v xcrun &> /dev/null; then
    echo "❌ Xcode command line tools not found. Install with: xcode-select --install"
    exit 1
fi

# Check for required environment variables
REQUIRED_VARS=(
    "APPLE_SIGNING_IDENTITY"
    "APPLE_ID"
    "APPLE_PASSWORD"
    "APPLE_TEAM_ID"
)

echo "🔍 Checking environment variables..."
for var in "${REQUIRED_VARS[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "❌ Missing required environment variable: $var"
        echo "   Please set it in your environment or .env file"
        exit 1
    else
        echo "✅ $var is set"
    fi
done

# Check if signing identity exists
echo "🔍 Checking signing identity..."
if security find-identity -v -p codesigning | grep -q "$APPLE_SIGNING_IDENTITY"; then
    echo "✅ Signing identity found: $APPLE_SIGNING_IDENTITY"
else
    echo "❌ Signing identity not found: $APPLE_SIGNING_IDENTITY"
    echo "   Available identities:"
    security find-identity -v -p codesigning
    exit 1
fi

# Build the app
echo "🔨 Building Colony app..."
cd "$(dirname "$0")/.."

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Build frontend
echo "🎨 Building frontend..."
npm run build

# Build Tauri app
echo "🦀 Building Tauri app..."
cd src-tauri
cargo tauri build --target aarch64-apple-darwin

# Find the built app
APP_PATH=$(find target/aarch64-apple-darwin/release/bundle/macos -name "*.app" | head -1)
if [[ -z "$APP_PATH" ]]; then
    echo "❌ No .app bundle found"
    exit 1
fi

echo "✅ Found app bundle: $APP_PATH"

# Test code signing
echo "🔏 Testing code signature..."
codesign -v -v --deep --strict "$APP_PATH"
if [[ $? -eq 0 ]]; then
    echo "✅ Code signature is valid"
else
    echo "❌ Code signature verification failed"
    exit 1
fi

# Test Gatekeeper
echo "🛡️  Testing Gatekeeper..."
spctl -a -v "$APP_PATH"
if [[ $? -eq 0 ]]; then
    echo "✅ Gatekeeper validation passed"
else
    echo "❌ Gatekeeper validation failed"
    exit 1
fi

# Create zip for notarization
echo "📦 Creating zip for notarization..."
APP_NAME=$(basename "$APP_PATH")
ZIP_PATH="${APP_PATH%/*}/${APP_NAME%.app}.zip"
ditto -c -k --keepParent "$APP_PATH" "$ZIP_PATH"
echo "✅ Created zip: $ZIP_PATH"

# Test notarization (if API key is available)
if [[ -n "$APP_STORE_CONNECT_API_KEY_ID" && -n "$APP_STORE_CONNECT_ISSUER_ID" ]]; then
    echo "🔔 Testing notarization..."
    
    # Check if API key file exists
    API_KEY_PATH="$HOME/.appstoreconnect/private_keys/AuthKey_$APP_STORE_CONNECT_API_KEY_ID.p8"
    if [[ -f "$API_KEY_PATH" ]]; then
        xcrun notarytool submit "$ZIP_PATH" \
            --key "$API_KEY_PATH" \
            --key-id "$APP_STORE_CONNECT_API_KEY_ID" \
            --issuer "$APP_STORE_CONNECT_ISSUER_ID" \
            --wait \
            --timeout 30m \
            --verbose
        
        if [[ $? -eq 0 ]]; then
            echo "✅ Notarization successful"
            
            # Staple the ticket
            echo "📎 Stapling notarization ticket..."
            xcrun stapler staple "$APP_PATH"
            
            # Verify stapling
            echo "🔍 Verifying stapled ticket..."
            xcrun stapler validate "$APP_PATH"
            
            if [[ $? -eq 0 ]]; then
                echo "✅ Notarization ticket stapled successfully"
            else
                echo "❌ Failed to staple notarization ticket"
                exit 1
            fi
        else
            echo "❌ Notarization failed"
            exit 1
        fi
    else
        echo "⚠️  API key not found at $API_KEY_PATH"
        echo "   Skipping notarization test"
    fi
else
    echo "⚠️  App Store Connect API credentials not set"
    echo "   Skipping notarization test"
fi

# Find and test DMG
DMG_PATH=$(find target/aarch64-apple-darwin/release/bundle/dmg -name "*.dmg" | head -1)
if [[ -n "$DMG_PATH" ]]; then
    echo "✅ Found DMG: $DMG_PATH"
    
    # Test DMG mounting
    echo "💿 Testing DMG mounting..."
    hdiutil attach "$DMG_PATH" -readonly -nobrowse
    if [[ $? -eq 0 ]]; then
        echo "✅ DMG mounts successfully"
        # Unmount
        hdiutil detach "/Volumes/Colony" 2>/dev/null || true
    else
        echo "❌ DMG mounting failed"
        exit 1
    fi
else
    echo "⚠️  No DMG found"
fi

echo ""
echo "🎉 All tests passed!"
echo "✅ Code signing: OK"
echo "✅ Gatekeeper: OK"
if [[ -n "$APP_STORE_CONNECT_API_KEY_ID" ]]; then
    echo "✅ Notarization: OK"
fi
echo "✅ DMG: OK"
echo ""
echo "Your Colony app is ready for distribution!"
