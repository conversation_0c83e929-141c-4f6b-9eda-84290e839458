#!/bin/bash

# Workflow Validation Script
# This script validates that all required files and configurations are in place

set -e

echo "🔍 Validating Colony macOS Signing Workflow"
echo "==========================================="

ERRORS=0
WARNINGS=0

# Function to report errors
error() {
    echo "❌ ERROR: $1"
    ((ERRORS++))
}

# Function to report warnings
warning() {
    echo "⚠️  WARNING: $1"
    ((WARNINGS++))
}

# Function to report success
success() {
    echo "✅ $1"
}

# Check if we're in the right directory
if [[ ! -f "src-tauri/tauri.conf.json" ]]; then
    error "Not in Colony project root directory"
    exit 1
fi

echo "📁 Checking project structure..."

# Check required files
REQUIRED_FILES=(
    ".github/workflows/release.yml"
    "src-tauri/tauri.conf.json"
    "src-tauri/entitlements.plist"
    "src-tauri/Info.plist"
    "MACOS_SIGNING_SETUP.md"
    "scripts/test_macos_signing.sh"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [[ -f "$file" ]]; then
        success "Found $file"
    else
        error "Missing required file: $file"
    fi
done

echo ""
echo "🔧 Checking Tauri configuration..."

# Check tauri.conf.json
if [[ -f "src-tauri/tauri.conf.json" ]]; then
    # Check bundle identifier
    if grep -q '"identifier": "com.colony.gui"' src-tauri/tauri.conf.json; then
        success "Bundle identifier is correctly set"
    else
        error "Bundle identifier is not set to com.colony.gui"
    fi
    
    # Check macOS signing configuration
    if grep -q '"hardenedRuntime": true' src-tauri/tauri.conf.json; then
        success "Hardened runtime is enabled"
    else
        error "Hardened runtime is not enabled"
    fi
    
    if grep -q '"entitlements": "entitlements.plist"' src-tauri/tauri.conf.json; then
        success "Entitlements file is configured"
    else
        error "Entitlements file is not configured"
    fi
fi

echo ""
echo "📋 Checking entitlements..."

if [[ -f "src-tauri/entitlements.plist" ]]; then
    # Check for required entitlements
    REQUIRED_ENTITLEMENTS=(
        "com.apple.security.app-sandbox"
        "com.apple.security.network.client"
        "com.apple.security.files.user-selected.read-write"
        "com.apple.security.cs.allow-jit"
    )
    
    for entitlement in "${REQUIRED_ENTITLEMENTS[@]}"; do
        if grep -q "$entitlement" src-tauri/entitlements.plist; then
            success "Found entitlement: $entitlement"
        else
            warning "Missing entitlement: $entitlement"
        fi
    done
fi

echo ""
echo "⚙️  Checking GitHub workflow..."

if [[ -f ".github/workflows/release.yml" ]]; then
    # Check for both macOS targets
    if grep -q "aarch64-apple-darwin" .github/workflows/release.yml; then
        success "Apple Silicon (aarch64) target configured"
    else
        error "Apple Silicon target not configured"
    fi
    
    if grep -q "x86_64-apple-darwin" .github/workflows/release.yml; then
        success "Intel (x86_64) target configured"
    else
        error "Intel target not configured"
    fi
    
    # Check for signing steps
    if grep -q "import-codesign-certs" .github/workflows/release.yml; then
        success "Certificate import step configured"
    else
        error "Certificate import step not configured"
    fi
    
    if grep -q "notarytool" .github/workflows/release.yml; then
        success "Notarization step configured"
    else
        error "Notarization step not configured"
    fi
    
    # Check for required secrets
    REQUIRED_SECRETS=(
        "APPLE_CERTIFICATE"
        "APPLE_CERTIFICATE_PASSWORD"
        "APPLE_SIGNING_IDENTITY"
        "APPLE_ID"
        "APPLE_PASSWORD"
        "APPLE_TEAM_ID"
        "APP_STORE_CONNECT_API_KEY"
        "APP_STORE_CONNECT_API_KEY_ID"
        "APP_STORE_CONNECT_ISSUER_ID"
    )
    
    echo ""
    echo "🔐 Checking for required secrets in workflow..."
    for secret in "${REQUIRED_SECRETS[@]}"; do
        if grep -q "secrets.$secret" .github/workflows/release.yml; then
            success "Secret referenced: $secret"
        else
            warning "Secret not referenced: $secret"
        fi
    done
fi

echo ""
echo "🏗️  Checking build configuration..."

# Check Cargo.toml for macOS targets
if [[ -f "src-tauri/Cargo.toml" ]]; then
    if grep -q "aarch64-apple-darwin" src-tauri/Cargo.toml; then
        success "Apple Silicon target in Cargo.toml"
    else
        warning "Apple Silicon target not in Cargo.toml"
    fi
    
    if grep -q "x86_64-apple-darwin" src-tauri/Cargo.toml; then
        success "Intel target in Cargo.toml"
    else
        warning "Intel target not in Cargo.toml"
    fi
fi

echo ""
echo "📊 Validation Summary"
echo "===================="

if [[ $ERRORS -eq 0 ]]; then
    success "No errors found!"
else
    echo "❌ Found $ERRORS error(s)"
fi

if [[ $WARNINGS -eq 0 ]]; then
    success "No warnings!"
else
    echo "⚠️  Found $WARNINGS warning(s)"
fi

echo ""
echo "📝 Next Steps:"
echo "1. Set up the required GitHub secrets (see MACOS_SIGNING_SETUP.md)"
echo "2. Generate and upload your Apple Developer certificates"
echo "3. Create an App Store Connect API key"
echo "4. Test the workflow by creating a release tag"
echo ""

if [[ $ERRORS -eq 0 ]]; then
    echo "🎉 Your workflow is ready for macOS signing and distribution!"
    exit 0
else
    echo "🔧 Please fix the errors above before proceeding."
    exit 1
fi
