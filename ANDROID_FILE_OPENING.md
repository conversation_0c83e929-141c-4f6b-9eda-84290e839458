# Android File Opening Implementation

This document explains how Colony implements cross-platform file opening functionality, specifically addressing the limitation that <PERSON><PERSON>'s `openPath` plugin is not supported on Android.

## Problem

The Tauri `openPath` plugin from `@tauri-apps/plugin-opener` is not supported on Android. This means that the standard way to open files with default applications doesn't work on Android devices.

## Solution

We implemented a custom Android plugin that uses Android's Intent system to open files with their default applications, while maintaining the existing desktop functionality.

## Implementation Details

### 1. Cross-Platform Utility Function

**File**: `src/utils/file/openFile.ts`

This utility function automatically detects the platform and uses the appropriate method:
- **Desktop**: Uses <PERSON><PERSON>'s `openPath` plugin
- **Android**: Uses our custom Android plugin

```typescript
export async function openFileWithDefaultApp(filePath: string, fileName?: string): Promise<void>
```

### 2. Android Native Implementation

**Files**:
- `src-tauri/gen/android/app/src/main/java/com/colony/gui/FileOpener.kt` - Native Android file opening logic
- `src-tauri/gen/android/app/src/main/java/com/colony/gui/MainActivity.kt` - JNI bridge

The Android implementation:
- Uses JNI bridge to call native Android code from Rust
- Uses `Intent.ACTION_VIEW` to open files
- Automatically detects MIME types based on file extensions
- Uses `FileProvider` for secure file access
- Falls back to a chooser dialog if no default app is found
- Handles errors gracefully

### 3. TCP Socket Communication

The solution uses TCP sockets on localhost to communicate between Rust and Android:
- MainActivity starts a TCP server on `127.0.0.1:8765`
- Rust connects to this TCP socket and sends the file path
- MainActivity receives the request and calls `FileOpener.openFileWithDefaultApp()`
- Result is returned back through the same socket connection
- This avoids JNI class loading issues that occur in background threads

### 4. FileProvider Configuration

**Files**: 
- `src-tauri/gen/android/app/src/main/AndroidManifest.xml`
- `src-tauri/gen/android/app/src/main/res/xml/file_paths.xml`

Configured to allow access to:
- Downloads directory (`/storage/emulated/0/Download`)
- External storage paths
- Cache directories

## Usage

### In Your Code

Replace direct `openPath` usage with the cross-platform utility:

```typescript
// Before (desktop only)
import { openPath } from '@tauri-apps/plugin-opener';
await openPath(filePath);

// After (cross-platform)
import { openFileWithDefaultApp } from '../utils/file/openFile';
await openFileWithDefaultApp(filePath, fileName);
```

### Command Structure

The implementation uses a simple Tauri command:
```typescript
await invoke('open_file_with_default_app', { filePath: '/path/to/file' });
```

The Rust command communicates with Android via TCP socket:
```rust
#[tauri::command]
async fn open_file_with_default_app(file_path: String, app: tauri::AppHandle) -> Result<String, String>
```

### Example Implementation

```typescript
async function openFile(file: FileObj) {
  const fullPath = `${file.downloadPath}/${file.name}`;
  await openFileWithDefaultApp(fullPath, file.name);
}
```

## How It Works

### Desktop Platforms
1. Uses Tauri's built-in `openPath` plugin
2. Leverages OS-native file opening mechanisms
3. Respects user's default application preferences

### Android Platform
1. Detects Android using user agent string
2. Calls Rust command `open_file_with_default_app`
3. Rust uses JNI to call native Android method
4. Android creates Intent with `ACTION_VIEW`
5. Uses FileProvider to create secure content URI
6. Starts activity to open file with default app
7. Falls back to chooser if no default app exists

## Supported File Types

The implementation supports all file types that have registered applications on the device:
- Documents (PDF, DOC, TXT, etc.)
- Images (JPG, PNG, GIF, etc.)
- Videos (MP4, AVI, etc.)
- Audio files (MP3, WAV, etc.)
- Archives (ZIP, RAR, etc.)
- Any other file type with a registered handler

## Error Handling

The implementation includes comprehensive error handling:
- File existence validation
- MIME type detection
- Intent resolution checking
- Graceful fallbacks
- User-friendly error messages via toast notifications

## Security Considerations

- Uses Android's FileProvider for secure file access
- Grants temporary read permissions only
- Follows Android security best practices
- No direct file path exposure to external apps

## Testing

To test the implementation:
1. Build the Android version of Colony
2. Download a file through the app
3. Navigate to Downloads page
4. Click on a file name to open it
5. Verify the file opens with the appropriate default application

## Future Enhancements

Potential improvements:
- Add support for opening files in specific applications
- Implement file sharing functionality
- Add preview capabilities for common file types
- Support for opening multiple files simultaneously
