<!-- src/components/StepWallet.svelte -->
<script lang="ts">

  export let walletPrivateKey: string = '';
  $: isValidPrivateKey = /^([a-fA-F0-9]{64})$/.test(walletPrivateKey || '');

  // Android detection
  const isAndroid = typeof window !== 'undefined' && /Android/i.test(navigator.userAgent);
</script>

<div>
  <h3 class="text-3xl font-extrabold dark:text-white" style="text-align: center;">Wallet</h3>
  <div class="row pt-3 pb-3">
    <p>
      Welcome! 🚀 If you plan on uploading content to the network, please enter your wallet's private key below.<br>
      <strong>You may <u>skip this step</u> by clicking Next if you just want to browse and download content from the network.</strong>
    </p>    <!-- <button class="btn">Default</button> -->
  </div>
  <div class="row pt-3 pb-3">
    {#if isAndroid}
      <!-- Android: Label on separate row -->
      <div class="android-wallet-container">
        <label class="android-label" for="wallet-private-key">Wallet Private Key:</label>
        <input
          id="wallet-private-key"
          bind:value={walletPrivateKey}
          type="text"
          class="input android-input"
          maxlength="64"
          spellcheck="false"
          autocomplete="off"
          placeholder="wallet private key" />
      </div>
    {:else}
      <!-- Desktop: Label and input on same row -->
      <label class="label" for="wallet-private-key">Wallet Private Key: </label>
      <input
        id="wallet-private-key"
        bind:value={walletPrivateKey}
        type="text"
        class="input"
        maxlength="64"
        spellcheck="false"
        autocomplete="off"
        placeholder="wallet private key" />
    {/if}
  </div>
  {#if walletPrivateKey && !isValidPrivateKey}
    <div class="row" style="color: red;">
      Private key must be exactly 64 hexadecimal characters (0-9, a-f).
    </div>
  {/if}
</div>

<style>
  .label {
    /* display: inline-block; */
    width: 140px; /* Fixed width for both labels */
    text-align: right;
    margin-right: 10px;
  }
  .row {
    display: flex;
    justify-content: center;
  }

  /* Android-specific styles */
  .android-wallet-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
    max-width: 400px;
  }

  .android-label {
    font-weight: 500;
    margin-bottom: 4px;
    text-align: left;
  }

  .android-input {
    width: 100%;
  }

</style>