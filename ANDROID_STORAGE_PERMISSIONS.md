# Android Storage Permissions for Colony

This document explains the storage permissions required for <PERSON> to access the Android Downloads directory and what users need to know about granting these permissions.

## Permissions Added to AndroidManifest.xml

The following permissions have been added to enable Downloads directory access:

```xml
<!-- External storage permissions for Downloads directory access -->
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

<!-- For Android 11+ (API 30+) - allows access to all files in external storage -->
<!-- Note: This requires user approval in device settings -->
<uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
```

Additionally, the application tag includes:
```xml
android:requestLegacyExternalStorage="true"
```

## What These Permissions Do

### READ_EXTERNAL_STORAGE & WRITE_EXTERNAL_STORAGE
- **Purpose**: Allows the app to read from and write to external storage
- **Required for**: Accessing the `/storage/emulated/0/Download` directory
- **User Experience**: On Android 6.0+ (API 23+), users will be prompted to grant these permissions

### MANAGE_EXTERNAL_STORAGE
- **Purpose**: Provides broader access to external storage on Android 11+ (API 30+)
- **Required for**: Full access to Downloads directory on modern Android versions
- **User Experience**: Requires manual approval in device settings (not a simple popup)

### requestLegacyExternalStorage
- **Purpose**: Maintains compatibility with pre-Android 11 storage behavior
- **Required for**: Ensuring consistent behavior across Android versions
- **User Experience**: Transparent to users, improves app compatibility

## User Permission Flow

### First App Launch
1. **Android 6.0-10 (API 23-29)**:
   - App will request storage permissions via popup dialogs
   - Users can grant or deny permissions
   - If denied, downloads will fail

2. **Android 11+ (API 30+)**:
   - App will request basic storage permissions via popup
   - For full Downloads access, users may need to manually enable "All files access" in settings

### Manual Permission Grant (Android 11+)

If users experience download issues on Android 11+, they may need to:

1. Go to **Settings** > **Apps** > **Colony**
2. Tap **Permissions**
3. Tap **Files and media** (or **Storage**)
4. Select **Allow management of all files**

## Testing Storage Permissions

### Verify Permissions Are Working

1. **Check Downloads Directory Access**:
   ```bash
   # Via ADB
   adb shell ls -la /storage/emulated/0/Download
   ```

2. **Test File Creation**:
   - Try downloading a file in the Colony app
   - Check if it appears in the Downloads folder
   - Verify file can be opened by other apps

### Permission Debugging

If downloads fail, check:

1. **Logcat for Permission Errors**:
   ```bash
   adb logcat | grep -i permission
   adb logcat | grep -i storage
   ```

2. **App Permissions in Settings**:
   - Verify storage permissions are granted
   - Check "All files access" on Android 11+

## Alternative Approaches

If users have issues with external storage permissions, consider these alternatives:

### Option 1: Use App-Specific Directory
- Path: `/Android/data/com.colony.gui/files/Download`
- Pros: No permissions required, always accessible
- Cons: Files not easily accessible to users, deleted when app is uninstalled

### Option 2: Use MediaStore API (Future Enhancement)
- Modern Android approach for file access
- Better user experience on Android 11+
- Requires code changes to use Android MediaStore APIs

## Troubleshooting Common Issues

### "Permission Denied" Errors
1. Check if storage permissions are granted in app settings
2. On Android 11+, verify "All files access" is enabled
3. Try uninstalling and reinstalling the app to reset permissions

### Downloads Not Appearing in Downloads Folder
1. Verify the download path is correct: `/storage/emulated/0/Download`
2. Check if files are being created in app-specific directories instead
3. Use a file manager app to browse and locate downloaded files

### App Crashes on Download
1. Check logcat for specific error messages
2. Verify AndroidManifest.xml permissions are correctly added
3. Test on different Android versions to isolate version-specific issues

## Build Considerations

### After Adding Permissions
1. **Clean and rebuild** the Android project:
   ```bash
   cargo tauri android build --release
   ```

2. **Uninstall previous version** before testing:
   ```bash
   adb uninstall com.colony.gui
   ```

3. **Install fresh APK** to ensure permissions are properly requested:
   ```bash
   adb install path/to/colony-signed.apk
   ```

### Version Compatibility
- **Minimum SDK**: 24 (Android 7.0) - Basic storage permissions work
- **Target SDK**: 34 (Android 14) - Full modern permission handling
- **Recommended Testing**: Test on Android 7, 10, 11, and 14 devices

## Security Considerations

### MANAGE_EXTERNAL_STORAGE Permission
- This is a **sensitive permission** that Google restricts
- Apps using this permission may face additional Play Store review
- Consider if your app truly needs full external storage access

### Best Practices
1. **Request permissions only when needed** (just before download)
2. **Explain to users why permissions are needed**
3. **Provide fallback options** if permissions are denied
4. **Test thoroughly** on different Android versions

## Future Recommendations

1. **Implement MediaStore API**: For better Android 11+ compatibility
2. **Add Permission Rationale**: Explain why storage access is needed
3. **Graceful Degradation**: Fallback to app-specific directories if permissions denied
4. **User Education**: Add in-app guidance about granting permissions

The current implementation should work for most users, but be prepared to help users navigate the permission settings on modern Android devices.
