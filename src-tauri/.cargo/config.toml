[target.x86_64-pc-windows-msvc]
rustflags = [
    "-C", "target-feature=+crt-static"
]

# Android cross-compilation configuration for RocksDB and other C++ dependencies
[target.aarch64-linux-android]
ar = "aarch64-linux-android-ar"
linker = "aarch64-linux-android-clang"

[target.armv7-linux-androideabi]
ar = "arm-linux-androideabi-ar"
linker = "arm-linux-androideabi-clang"

[target.i686-linux-android]
ar = "i686-linux-android-ar"
linker = "i686-linux-android-clang"

[target.x86_64-linux-android]
ar = "x86_64-linux-android-ar"
linker = "x86_64-linux-android-clang"
