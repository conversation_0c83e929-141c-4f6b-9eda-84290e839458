# Privacy Policy for Colony

**Effective Date:** January 1, 2025  
**Last Updated:** January 1, 2025

## Overview

Colony is a decentralized file management and semantic search application that operates on the Autonomi network. This privacy policy explains how Colony handles your data and protects your privacy.

## Data Collection

**Colony does not collect, store, or transmit any personal data to external servers.**

Colony operates as a fully client-side application that:
- Runs entirely on your local device
- Does not have any backend servers or databases
- Does not collect analytics, telemetry, or usage data
- Does not require user accounts or registration
- Does not track user behavior or activities

## How Colony Works

Colony is a desktop application that:

1. **Connects directly to the Autonomi network** - a decentralized, peer-to-peer storage network
2. **Stores files on the decentralized network** - files are encrypted and distributed across network nodes
3. **Performs searches locally** - all search operations happen on your device
4. **Manages your wallet locally** - your private keys and wallet information remain on your device

## Data Storage

### Local Data
Colony may store the following data locally on your device:
- Application preferences and settings
- Your Autonomi network wallet private keys (encrypted)
- File metadata and search indexes
- Downloaded file cache

### Network Data
When you upload files to the Autonomi network:
- Files are encrypted before leaving your device
- Files are stored in a distributed manner across the Autonomi network
- Only you have the keys to decrypt your files
- Colony does not have access to your encrypted files

## Third-Party Services

Colony does not integrate with any third-party analytics, advertising, or tracking services.

The only external network Colony connects to is the **Autonomi network**, which is:
- A decentralized, peer-to-peer network
- Not controlled by Colony or any single entity
- Designed to protect user privacy through encryption and decentralization

## Data Security

- All sensitive data (wallet keys, etc.) is encrypted before being stored locally
- Files uploaded to the Autonomi network are encrypted client-side before transmission
- Colony uses industry-standard encryption practices
- No data is transmitted to Colony's developers or any centralized servers

## Children's Privacy

Colony does not knowingly collect any information from children under 13 years of age. Since Colony does not collect personal information from any users, this policy applies to users of all ages.

## Changes to This Policy

We may update this privacy policy from time to time. Any changes will be reflected in the "Last Updated" date above. Since Colony does not collect contact information, we recommend checking this policy periodically for updates.

## Contact Information

If you have questions about this privacy policy, you can:
- Open an issue on our GitHub repository: https://github.com/zettawatt/colony
- Review our open-source code to verify our privacy practices

## Open Source Transparency

Colony is open-source software. You can review our complete source code to verify that:
- No data collection mechanisms are present
- No external tracking or analytics are implemented
- All operations are performed locally or on the decentralized Autonomi network

**Repository:** https://github.com/zettawatt/colony

## Summary

Colony is designed with privacy as a fundamental principle:
- ✅ No data collection
- ✅ No external servers
- ✅ No user tracking
- ✅ No analytics
- ✅ Fully decentralized operation
- ✅ Open-source and verifiable

Your privacy is protected because Colony simply doesn't collect, store, or have access to your personal information.
